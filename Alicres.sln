Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.5.2.0
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "examples", "examples", "{B36A84DF-456D-A817-6EDD-3EC3E7F6E11F}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{827E0CD3-B72D-47B6-A68D-7590B98EB39B}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tests", "tests", "{0AB3BF05-4346-4AA6-1389-037BE0695223}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Alicres.SerialPort.Examples", "examples\Alicres.SerialPort.Examples\Alicres.SerialPort.Examples.csproj", "{4CAF9A88-35A5-A9BD-E73F-A32445061725}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Alicres.SerialPort", "src\Alicres.SerialPort\Alicres.SerialPort.csproj", "{35EE61F7-3CE7-6836-5039-529F38841BEB}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Alicres.SerialPort.Tests", "tests\Alicres.SerialPort.Tests\Alicres.SerialPort.Tests.csproj", "{6BD2B43A-A962-AAED-3A59-244244F60414}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Alicres.Protocol", "src\Alicres.Protocol\Alicres.Protocol.csproj", "{5C9D9C5E-9C5E-5C9D-9C5E-9C5E9C5E9C5E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Alicres.Protocol.Tests", "tests\Alicres.Protocol.Tests\Alicres.Protocol.Tests.csproj", "{6D0E0D6F-0D6F-6D0E-0D6F-0D6F0D6F0D6F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Alicres.Protocol.Examples", "examples\Alicres.Protocol.Examples\Alicres.Protocol.Examples.csproj", "{7E1F1E70-1E70-7E1F-1E70-1E701E701E70}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Alicres.SerialPort.QuickStart", "examples\Alicres.SerialPort.QuickStart\Alicres.SerialPort.QuickStart.csproj", "{8F2G2F81-2F81-8F2G-2F81-2F812F812F81}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{4CAF9A88-35A5-A9BD-E73F-A32445061725}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4CAF9A88-35A5-A9BD-E73F-A32445061725}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4CAF9A88-35A5-A9BD-E73F-A32445061725}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4CAF9A88-35A5-A9BD-E73F-A32445061725}.Release|Any CPU.Build.0 = Release|Any CPU
		{35EE61F7-3CE7-6836-5039-529F38841BEB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{35EE61F7-3CE7-6836-5039-529F38841BEB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{35EE61F7-3CE7-6836-5039-529F38841BEB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{35EE61F7-3CE7-6836-5039-529F38841BEB}.Release|Any CPU.Build.0 = Release|Any CPU
		{6BD2B43A-A962-AAED-3A59-244244F60414}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6BD2B43A-A962-AAED-3A59-244244F60414}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6BD2B43A-A962-AAED-3A59-244244F60414}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6BD2B43A-A962-AAED-3A59-244244F60414}.Release|Any CPU.Build.0 = Release|Any CPU
		{5C9D9C5E-9C5E-5C9D-9C5E-9C5E9C5E9C5E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5C9D9C5E-9C5E-5C9D-9C5E-9C5E9C5E9C5E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5C9D9C5E-9C5E-5C9D-9C5E-9C5E9C5E9C5E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5C9D9C5E-9C5E-5C9D-9C5E-9C5E9C5E9C5E}.Release|Any CPU.Build.0 = Release|Any CPU
		{6D0E0D6F-0D6F-6D0E-0D6F-0D6F0D6F0D6F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6D0E0D6F-0D6F-6D0E-0D6F-0D6F0D6F0D6F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6D0E0D6F-0D6F-6D0E-0D6F-0D6F0D6F0D6F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6D0E0D6F-0D6F-6D0E-0D6F-0D6F0D6F0D6F}.Release|Any CPU.Build.0 = Release|Any CPU
		{7E1F1E70-1E70-7E1F-1E70-1E701E701E70}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7E1F1E70-1E70-7E1F-1E70-1E701E701E70}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7E1F1E70-1E70-7E1F-1E70-1E701E701E70}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7E1F1E70-1E70-7E1F-1E70-1E701E701E70}.Release|Any CPU.Build.0 = Release|Any CPU
		{8F2G2F81-2F81-8F2G-2F81-2F812F812F81}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8F2G2F81-2F81-8F2G-2F81-2F812F812F81}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8F2G2F81-2F81-8F2G-2F81-2F812F812F81}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8F2G2F81-2F81-8F2G-2F81-2F812F812F81}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{4CAF9A88-35A5-A9BD-E73F-A32445061725} = {B36A84DF-456D-A817-6EDD-3EC3E7F6E11F}
		{35EE61F7-3CE7-6836-5039-529F38841BEB} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
		{6BD2B43A-A962-AAED-3A59-244244F60414} = {0AB3BF05-4346-4AA6-1389-037BE0695223}
		{5C9D9C5E-9C5E-5C9D-9C5E-9C5E9C5E9C5E} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
		{6D0E0D6F-0D6F-6D0E-0D6F-0D6F0D6F0D6F} = {0AB3BF05-4346-4AA6-1389-037BE0695223}
		{7E1F1E70-1E70-7E1F-1E70-1E701E701E70} = {B36A84DF-456D-A817-6EDD-3EC3E7F6E11F}
		{8F2G2F81-2F81-8F2G-2F81-2F812F812F81} = {B36A84DF-456D-A817-6EDD-3EC3E7F6E11F}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {14921368-C9C8-4861-82EB-2BCC1AF37893}
	EndGlobalSection
EndGlobal
