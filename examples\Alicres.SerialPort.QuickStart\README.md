# Alicres.SerialPort 快速入门示例

本示例项目演示了如何正确使用 Alicres.SerialPort 库，特别是解决构造函数参数类型不匹配的问题。

## 🚨 常见问题解决

### 问题：构造函数参数类型不匹配

**错误信息：**
```
错误代码：CS1503
错误描述：参数 1: 无法从"Alicres.SerialPort.Models.SerialPortConfiguration"转换为"Microsoft.Extensions.Logging.ILogger<Alicres.SerialPort.Services.SerialPortService>"
```

**原因分析：**
- `SerialPortService` 的所有构造函数都需要 `ILogger<SerialPortService>` 参数
- 快速入门指南中的示例代码缺少了 logger 的创建过程

**解决方案：**

#### 方法一：直接创建 Logger（推荐用于简单场景）

```csharp
using Alicres.SerialPort.Models;
using Alicres.SerialPort.Services;
using Microsoft.Extensions.Logging;

// 创建日志记录器
using var loggerFactory = LoggerFactory.Create(builder =>
{
    builder.AddConsole().SetMinimumLevel(LogLevel.Information);
});
var logger = loggerFactory.CreateLogger<SerialPortService>();

// 创建配置
var config = new SerialPortConfiguration
{
    PortName = "COM1",
    BaudRate = 9600,
    EnableAutoReconnect = true
};

// 创建服务（注意参数顺序：配置在前，日志记录器在后）
using var serialPort = new SerialPortService(config, logger);
```

#### 方法二：先创建服务，后配置参数

```csharp
// 创建简单的控制台日志记录器
using var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
var logger = loggerFactory.CreateLogger<SerialPortService>();

// 先创建服务实例（只传入日志记录器）
using var serialPort = new SerialPortService(logger);

// 然后配置串口参数
var config = new SerialPortConfiguration
{
    PortName = "COM1",
    BaudRate = 9600,
    EnableAutoReconnect = true
};

serialPort.Configure(config);
```

#### 方法三：依赖注入方式（推荐用于生产环境）

```csharp
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Alicres.SerialPort.Extensions;

var services = new ServiceCollection();

// 添加日志记录
services.AddLogging(builder => builder.AddConsole());

// 注册 Alicres 串口服务
services.AddAlicresSerialPort();

// 构建服务提供程序
using var serviceProvider = services.BuildServiceProvider();

// 获取串口管理器
var manager = serviceProvider.GetRequiredService<ISerialPortManager>();

// 创建串口服务
var serialPort = manager.CreateSerialPort(config);
```

## 🏃‍♂️ 运行示例

1. 确保您的系统中有可用的串口（或使用虚拟串口工具）
2. 修改示例代码中的端口名称（COM1、COM2、COM3）为您系统中实际存在的端口
3. 运行项目：

```bash
cd examples/Alicres.SerialPort.QuickStart
dotnet run
```

## 📋 构造函数签名参考

`SerialPortService` 类提供了两个构造函数：

### 构造函数1：仅日志记录器
```csharp
public SerialPortService(ILogger<SerialPortService> logger)
```
- 使用默认配置创建服务
- 需要后续调用 `Configure()` 方法设置串口参数

### 构造函数2：配置 + 日志记录器
```csharp
public SerialPortService(SerialPortConfiguration configuration, ILogger<SerialPortService> logger)
```
- 直接使用指定配置创建服务
- 立即可用，无需额外配置

## 🔧 必需的 NuGet 包

如果您在自己的项目中使用 Alicres.SerialPort，请确保安装以下包：

```xml
<PackageReference Include="Alicres.SerialPort" Version="1.1.0" />
<PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.0" />
<PackageReference Include="Microsoft.Extensions.Logging.Console" Version="8.0.0" />
```

对于依赖注入场景，还需要：
```xml
<PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
```

## 💡 最佳实践

1. **生产环境**：推荐使用依赖注入方式，便于测试和维护
2. **简单应用**：可以直接创建 Logger 和服务实例
3. **日志级别**：根据需要调整日志级别，开发时使用 `Debug`，生产时使用 `Information` 或更高级别
4. **资源释放**：始终使用 `using` 语句确保资源正确释放

## 🐛 故障排除

- **端口不存在**：检查系统中是否有指定的串口
- **端口被占用**：确保没有其他程序正在使用该串口
- **权限问题**：在某些系统上可能需要管理员权限访问串口
- **驱动问题**：确保串口设备的驱动程序已正确安装
