# 基本串口通讯示例

本示例演示如何使用 Alicres.SerialPort 实现基本的串口通讯功能。

## 📋 示例列表

- [简单数据收发](#简单数据收发)
- [事件驱动通讯](#事件驱动通讯)
- [自动重连机制](#自动重连机制)
- [数据格式转换](#数据格式转换)
- [批量数据处理](#批量数据处理)

---

## 📡 简单数据收发

### 基础发送接收示例

```csharp
using Alicres.SerialPort.Models;
using Alicres.SerialPort.Services;
using Microsoft.Extensions.Logging;

class BasicCommunicationExample
{
    public static async Task Main(string[] args)
    {
        // 创建日志记录器
        using var loggerFactory = LoggerFactory.Create(builder =>
            builder.AddConsole().SetMinimumLevel(LogLevel.Information));
        var logger = loggerFactory.CreateLogger<SerialPortService>();

        // 配置串口参数
        var config = new SerialPortConfiguration
        {
            PortName = "COM1",        // 根据实际情况修改
            BaudRate = 9600,
            DataBits = 8,
            StopBits = StopBits.One,
            Parity = Parity.None,
            ReadTimeout = 2000,
            WriteTimeout = 2000
        };

        // 创建串口服务
        using var serialPort = new SerialPortService(config, logger);

        try
        {
            Console.WriteLine("正在打开串口...");
            await serialPort.OpenAsync();
            Console.WriteLine($"串口 {config.PortName} 已打开");

            // 发送文本数据
            string textToSend = "Hello, Serial Port!";
            await serialPort.SendTextAsync(textToSend);
            Console.WriteLine($"已发送: {textToSend}");

            // 发送十六进制数据
            var hexData = SerialPortData.FromHexString("48656C6C6F"); // "Hello"
            await serialPort.SendAsync(hexData);
            Console.WriteLine($"已发送十六进制: {hexData.ToHexString()}");

            // 等待响应
            Console.WriteLine("等待响应数据...");
            await Task.Delay(3000);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"错误: {ex.Message}");
        }
        finally
        {
            await serialPort.CloseAsync();
            Console.WriteLine("串口已关闭");
        }
    }
}
```

### 同步发送异步接收

```csharp
public class SyncSendAsyncReceive
{
    private readonly SerialPortService _serialPort;
    private readonly List<SerialPortData> _receivedData = new();

    public SyncSendAsyncReceive(SerialPortConfiguration config)
    {
        _serialPort = new SerialPortService(config);
        
        // 订阅数据接收事件
        _serialPort.DataReceived += OnDataReceived;
    }

    private void OnDataReceived(object sender, SerialPortDataReceivedEventArgs e)
    {
        _receivedData.Add(e.Data);
        Console.WriteLine($"接收到数据: {e.Data.ToText()}");
        Console.WriteLine($"十六进制: {e.Data.ToHexString()}");
        Console.WriteLine($"时间戳: {e.Timestamp:HH:mm:ss.fff}");
        Console.WriteLine();
    }

    public async Task RunExample()
    {
        await _serialPort.OpenAsync();

        // 发送一系列命令
        var commands = new[]
        {
            "AT",
            "AT+VERSION",
            "AT+RESET"
        };

        foreach (var command in commands)
        {
            Console.WriteLine($"发送命令: {command}");
            await _serialPort.SendTextAsync(command + "\r\n");

            // 等待响应
            await Task.Delay(1000);
        }

        // 显示接收到的所有数据
        Console.WriteLine($"\n总共接收到 {_receivedData.Count} 条数据");
        
        await _serialPort.CloseAsync();
    }
}
```

---

## 🎯 事件驱动通讯

### 完整事件处理示例

```csharp
public class EventDrivenCommunication
{
    private readonly SerialPortService _serialPort;
    private readonly Queue<string> _commandQueue = new();
    private bool _isProcessingCommands = false;

    public EventDrivenCommunication(SerialPortConfiguration config)
    {
        _serialPort = new SerialPortService(config);
        SetupEventHandlers();
    }

    private void SetupEventHandlers()
    {
        // 数据接收事件
        _serialPort.DataReceived += async (sender, e) =>
        {
            var response = e.Data.ToText().Trim();
            Console.WriteLine($"[{e.Timestamp:HH:mm:ss}] 接收: {response}");

            // 根据响应内容处理
            await ProcessResponse(response);
        };

        // 状态变化事件
        _serialPort.StatusChanged += (sender, e) =>
        {
            Console.WriteLine($"状态变化: {e.PreviousState} -> {e.CurrentState}");

            if (e.CurrentState == SerialPortConnectionState.Connected)
            {
                Console.WriteLine("✅ 连接成功，开始处理命令队列");
                _ = Task.Run(ProcessCommandQueue);
            }
        };

        // 错误处理事件
        _serialPort.ErrorOccurred += (sender, e) =>
        {
            Console.WriteLine($"❌ 错误: {e.Exception.Message}");
            Console.WriteLine($"错误级别: {e.ErrorLevel}");
            Console.WriteLine($"端口: {e.PortName}");
            Console.WriteLine($"时间: {e.Timestamp:HH:mm:ss.fff}");

            // 根据异常类型采取不同的处理策略
            if (e.Exception is TimeoutException)
            {
                Console.WriteLine("超时错误，继续等待");
            }
            else if (e.Exception.Message.Contains("连接"))
            {
                Console.WriteLine("连接相关错误，将尝试重连");
            }
            else
            {
                Console.WriteLine("其他错误，记录日志");
            }
        };
    }

    public async Task AddCommand(string command)
    {
        _commandQueue.Enqueue(command);
        Console.WriteLine($"命令已加入队列: {command}");

        if (!_isProcessingCommands && _serialPort.IsConnected)
        {
            await ProcessCommandQueue();
        }
    }

    private async Task ProcessCommandQueue()
    {
        if (_isProcessingCommands) return;
        
        _isProcessingCommands = true;

        try
        {
            while (_commandQueue.Count > 0)
            {
                var command = _commandQueue.Dequeue();
                Console.WriteLine($"发送命令: {command}");

                await _serialPort.SendTextAsync(command + "\r\n");

                // 等待响应
                await Task.Delay(500);
            }
        }
        finally
        {
            _isProcessingCommands = false;
        }
    }

    private async Task ProcessResponse(string response)
    {
        // 根据响应内容执行相应操作
        if (response.Contains("OK"))
        {
            Console.WriteLine("✅ 命令执行成功");
        }
        else if (response.Contains("ERROR"))
        {
            Console.WriteLine("❌ 命令执行失败");
        }
        else if (response.Contains("DATA:"))
        {
            var data = response.Substring(5);
            Console.WriteLine($"📊 接收到数据: {data}");
            await ProcessReceivedData(data);
        }
    }

    private async Task ProcessReceivedData(string data)
    {
        // 处理接收到的数据
        await Task.Delay(10); // 模拟数据处理
        Console.WriteLine($"数据处理完成: {data}");
    }

    public async Task StartCommunication()
    {
        await _serialPort.OpenAsync();
    }

    public async Task StopCommunication()
    {
        await _serialPort.CloseAsync();
    }
}

// 使用示例
class Program
{
    static async Task Main(string[] args)
    {
        var config = new SerialPortConfiguration
        {
            PortName = "COM1",
            BaudRate = 115200,
            EnableAutoReconnect = true
        };

        var communication = new EventDrivenCommunication(config);
        
        await communication.StartCommunication();

        // 添加一些测试命令
        await communication.AddCommand("AT");
        await communication.AddCommand("AT+VERSION");
        await communication.AddCommand("AT+DATA?");

        Console.WriteLine("按任意键退出...");
        Console.ReadKey();

        await communication.StopCommunication();
    }
}
```

---

## 🔄 自动重连机制

### 智能重连示例

```csharp
public class AutoReconnectExample
{
    private readonly SerialPortService _serialPort;
    private readonly Timer _heartbeatTimer;
    private DateTime _lastDataReceived = DateTime.Now;

    public AutoReconnectExample(SerialPortConfiguration config)
    {
        // 配置自动重连
        config.EnableAutoReconnect = true;
        config.ReconnectInterval = 3000;        // 3秒重连间隔
        config.MaxReconnectAttempts = 5;        // 最多重连5次
        config.ReconnectBackoffMultiplier = 1.5; // 重连间隔递增

        _serialPort = new SerialPortService(config);
        SetupEventHandlers();

        // 设置心跳检测
        _heartbeatTimer = new Timer(CheckHeartbeat, null, 
            TimeSpan.FromSeconds(10), TimeSpan.FromSeconds(10));
    }

    private void SetupEventHandlers()
    {
        _serialPort.DataReceived += (sender, e) =>
        {
            _lastDataReceived = DateTime.Now;
            Console.WriteLine($"接收到数据: {e.Data.ToText()}");
        };

        _serialPort.StatusChanged += (sender, e) =>
        {
            Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] 状态: {e.CurrentState}");

            switch (e.CurrentState)
            {
                case SerialPortConnectionState.Connected:
                    Console.WriteLine("✅ 连接成功");
                    _ = Task.Run(StartHeartbeat);
                    break;

                case SerialPortConnectionState.Disconnected:
                    Console.WriteLine("❌ 连接断开");
                    break;

                case SerialPortConnectionState.Reconnecting:
                    Console.WriteLine("🔄 正在重连...");
                    break;
            }
        };

        // 注意：SerialPortService 的自动重连是内部处理的
        // 可以通过 StatusChanged 事件监控重连状态
        // 重连相关的详细信息可以通过日志查看

        _serialPort.ErrorOccurred += (sender, e) =>
        {
            Console.WriteLine($"错误: {e.ErrorLevel} - {e.Exception.Message}");
            Console.WriteLine($"端口: {e.PortName}, 时间: {e.Timestamp:HH:mm:ss.fff}");
        };
    }

    private async Task StartHeartbeat()
    {
        while (_serialPort.IsConnected)
        {
            try
            {
                // 发送心跳包
                await _serialPort.SendTextAsync("PING\r\n");
                Console.WriteLine("💓 发送心跳");

                await Task.Delay(5000); // 5秒间隔
            }
            catch (Exception ex)
            {
                Console.WriteLine($"心跳发送失败: {ex.Message}");
                break;
            }
        }
    }

    private void CheckHeartbeat(object state)
    {
        var timeSinceLastData = DateTime.Now - _lastDataReceived;
        
        if (timeSinceLastData.TotalSeconds > 30) // 30秒无数据
        {
            Console.WriteLine("⚠️ 心跳超时，可能连接异常");
            
            if (_serialPort.IsConnected)
            {
                Console.WriteLine("尝试重新建立连接");
                _ = Task.Run(async () =>
                {
                    await _serialPort.CloseAsync();
                    await Task.Delay(1000);
                    await _serialPort.OpenAsync();
                });
            }
        }
    }

    public async Task RunExample()
    {
        Console.WriteLine("启动自动重连示例");
        
        try
        {
            await _serialPort.OpenAsync();
            
            Console.WriteLine("连接已建立，模拟运行30秒...");
            Console.WriteLine("可以拔插串口设备来测试自动重连功能");
            
            await Task.Delay(30000);
        }
        finally
        {
            _heartbeatTimer?.Dispose();
            await _serialPort.CloseAsync();
        }
    }
}
```

---

## 🔄 数据格式转换

### 多格式数据处理

```csharp
public class DataFormatExample
{
    public static async Task RunExample()
    {
        var config = new SerialPortConfiguration
        {
            PortName = "COM1",
            BaudRate = 9600
        };

        using var serialPort = new SerialPortService(config);

        // 订阅数据接收事件
        serialPort.DataReceived += (sender, e) =>
        {
            var data = e.Data;
            
            Console.WriteLine("=== 接收到数据 ===");
            Console.WriteLine($"原始字节数: {data.Length}");
            Console.WriteLine($"十六进制: {data.ToHexString()}");
            Console.WriteLine($"十六进制(空格分隔): {data.ToHexString(" ")}");
            Console.WriteLine($"十六进制(大写): {data.ToHexString("-", true)}");
            Console.WriteLine($"文本(UTF-8): {data.ToText()}");
            Console.WriteLine($"文本(ASCII): {data.ToText(Encoding.ASCII)}");
            
            // 获取原始字节数组
            var bytes = data.GetDataCopy();
            Console.WriteLine($"字节数组: [{string.Join(", ", bytes.Select(b => $"0x{b:X2}"))}]");
            Console.WriteLine();
        };

        await serialPort.OpenAsync();

        // 发送不同格式的数据
        await SendDifferentFormats(serialPort);

        Console.WriteLine("按任意键退出...");
        Console.ReadKey();

        await serialPort.CloseAsync();
    }

    private static async Task SendDifferentFormats(SerialPortService serialPort)
    {
        Console.WriteLine("=== 发送不同格式的数据 ===");

        // 1. 发送文本字符串
        string textData = "Hello World!";
        await serialPort.SendTextAsync(textData);
        Console.WriteLine($"发送文本: {textData}");

        await Task.Delay(500);

        // 2. 发送十六进制字符串
        string hexString = "48656C6C6F20576F726C6421"; // "Hello World!"
        var hexData = SerialPortData.FromHexString(hexString);
        await serialPort.SendAsync(hexData);
        Console.WriteLine($"发送十六进制: {hexString}");

        await Task.Delay(500);

        // 3. 发送字节数组
        byte[] byteArray = { 0x48, 0x65, 0x6C, 0x6C, 0x6F, 0x21 }; // "Hello!"
        var byteData = new SerialPortData(byteArray);
        await serialPort.SendAsync(byteData);
        Console.WriteLine($"发送字节数组: [{string.Join(", ", byteArray.Select(b => $"0x{b:X2}"))}]");

        await Task.Delay(500);

        // 4. 发送控制字符
        var controlData = new SerialPortData(new byte[] { 0x02, 0x48, 0x65, 0x6C, 0x6C, 0x6F, 0x03 }); // STX + "Hello" + ETX
        await serialPort.SendAsync(controlData);
        Console.WriteLine("发送控制字符: STX + Hello + ETX");

        await Task.Delay(500);

        // 5. 发送结构化数据
        await SendStructuredData(serialPort);
    }

    private static async Task SendStructuredData(SerialPortService serialPort)
    {
        // 模拟发送结构化数据包
        var packet = new DataPacket
        {
            Header = 0xAA,
            Command = 0x01,
            Length = 4,
            Data = new byte[] { 0x12, 0x34, 0x56, 0x78 },
            Checksum = 0x00 // 稍后计算
        };

        // 计算校验和
        packet.Checksum = CalculateChecksum(packet);

        // 转换为字节数组
        var packetBytes = PacketToBytes(packet);
        var packetData = new SerialPortData(packetBytes);

        await serialPort.SendAsync(packetData);
        Console.WriteLine($"发送数据包: {packetData.ToHexString(" ")}");
    }

    private static byte CalculateChecksum(DataPacket packet)
    {
        byte checksum = 0;
        checksum ^= packet.Header;
        checksum ^= packet.Command;
        checksum ^= packet.Length;
        foreach (byte b in packet.Data)
        {
            checksum ^= b;
        }
        return checksum;
    }

    private static byte[] PacketToBytes(DataPacket packet)
    {
        var bytes = new List<byte>
        {
            packet.Header,
            packet.Command,
            packet.Length
        };
        bytes.AddRange(packet.Data);
        bytes.Add(packet.Checksum);
        return bytes.ToArray();
    }

    private class DataPacket
    {
        public byte Header { get; set; }
        public byte Command { get; set; }
        public byte Length { get; set; }
        public byte[] Data { get; set; }
        public byte Checksum { get; set; }
    }
}
```

---

## 📦 批量数据处理

### 高效批量处理示例

```csharp
public class BatchProcessingExample
{
    private readonly SerialPortService _serialPort;
    private readonly List<SerialPortData> _dataBuffer = new();
    private readonly Timer _batchTimer;
    private readonly SemaphoreSlim _processingLock = new(1, 1);
    
    private const int BatchSize = 10;
    private const int BatchTimeoutMs = 1000;

    public BatchProcessingExample(SerialPortConfiguration config)
    {
        _serialPort = new SerialPortService(config);
        
        // 设置批量处理定时器
        _batchTimer = new Timer(ProcessBatchTimeout, null, 
            BatchTimeoutMs, BatchTimeoutMs);
        
        SetupEventHandlers();
    }

    private void SetupEventHandlers()
    {
        _serialPort.DataReceived += async (sender, e) =>
        {
            await AddToBuffer(e.Data);
        };
    }

    private async Task AddToBuffer(SerialPortData data)
    {
        await _processingLock.WaitAsync();
        try
        {
            _dataBuffer.Add(data);
            Console.WriteLine($"缓冲区数据: {_dataBuffer.Count}/{BatchSize}");

            // 达到批量大小时立即处理
            if (_dataBuffer.Count >= BatchSize)
            {
                await ProcessBatch();
            }
        }
        finally
        {
            _processingLock.Release();
        }
    }

    private async void ProcessBatchTimeout(object state)
    {
        await _processingLock.WaitAsync();
        try
        {
            if (_dataBuffer.Count > 0)
            {
                Console.WriteLine("批量处理超时，处理剩余数据");
                await ProcessBatch();
            }
        }
        finally
        {
            _processingLock.Release();
        }
    }

    private async Task ProcessBatch()
    {
        if (_dataBuffer.Count == 0) return;

        var batchData = _dataBuffer.ToArray();
        _dataBuffer.Clear();

        Console.WriteLine($"=== 处理批量数据 ({batchData.Length} 项) ===");

        // 并行处理批量数据
        var tasks = batchData.Select(async (data, index) =>
        {
            await ProcessSingleData(data, index);
        });

        await Task.WhenAll(tasks);

        Console.WriteLine("批量处理完成\n");
    }

    private async Task ProcessSingleData(SerialPortData data, int index)
    {
        // 模拟数据处理
        await Task.Delay(50);
        
        Console.WriteLine($"  [{index}] 处理数据: {data.ToText().Trim()} " +
                         $"({data.Length} 字节)");
    }

    public async Task RunExample()
    {
        await _serialPort.OpenAsync();

        Console.WriteLine("批量处理示例启动");
        Console.WriteLine($"批量大小: {BatchSize}");
        Console.WriteLine($"超时时间: {BatchTimeoutMs} ms");
        Console.WriteLine();

        // 模拟发送多条数据
        for (int i = 1; i <= 25; i++)
        {
            await _serialPort.SendTextAsync($"Data {i:D2}\r\n");
            await Task.Delay(200); // 模拟数据间隔
        }

        // 等待所有数据处理完成
        await Task.Delay(3000);

        _batchTimer?.Dispose();
        await _serialPort.CloseAsync();
    }
}
```

---

## 🚀 运行示例

### 完整的控制台应用程序

```csharp
class Program
{
    static async Task Main(string[] args)
    {
        Console.WriteLine("Alicres.SerialPort 示例程序");
        Console.WriteLine("请选择要运行的示例:");
        Console.WriteLine("1. 基本通讯");
        Console.WriteLine("2. 事件驱动通讯");
        Console.WriteLine("3. 自动重连");
        Console.WriteLine("4. 数据格式转换");
        Console.WriteLine("5. 批量数据处理");
        Console.Write("请输入选择 (1-5): ");

        var choice = Console.ReadLine();
        
        var config = new SerialPortConfiguration
        {
            PortName = GetPortName(),
            BaudRate = 9600,
            EnableAutoReconnect = true
        };

        try
        {
            switch (choice)
            {
                case "1":
                    await RunBasicCommunication(config);
                    break;
                case "2":
                    await RunEventDrivenCommunication(config);
                    break;
                case "3":
                    await RunAutoReconnectExample(config);
                    break;
                case "4":
                    await DataFormatExample.RunExample();
                    break;
                case "5":
                    await RunBatchProcessingExample(config);
                    break;
                default:
                    Console.WriteLine("无效选择");
                    break;
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"示例运行错误: {ex.Message}");
        }

        Console.WriteLine("\n示例运行完成，按任意键退出...");
        Console.ReadKey();
    }

    private static string GetPortName()
    {
        var availablePorts = SerialPortService.GetAvailablePorts();
        
        if (availablePorts.Length == 0)
        {
            Console.WriteLine("未找到可用的串口");
            return "COM1"; // 默认值
        }

        Console.WriteLine($"可用串口: {string.Join(", ", availablePorts)}");
        Console.Write($"请输入串口名称 (默认: {availablePorts[0]}): ");
        
        var input = Console.ReadLine();
        return string.IsNullOrEmpty(input) ? availablePorts[0] : input;
    }

    // 其他示例运行方法...
}
```

这些示例展示了 Alicres.SerialPort 的各种使用场景，从基本的数据收发到高级的批量处理和自动重连功能。每个示例都包含详细的注释和错误处理，可以直接运行或作为您项目的参考代码。
