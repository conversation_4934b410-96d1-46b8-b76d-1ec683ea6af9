using Alicres.SerialPort.Models;
using Alicres.SerialPort.Services;
using Microsoft.Extensions.Logging;

namespace Alicres.SerialPort.QuickStart;

/// <summary>
/// Alicres.SerialPort 快速入门示例
/// </summary>
class Program
{
    static async Task Main(string[] args)
    {
        Console.WriteLine("=== Alicres.SerialPort 快速入门示例 ===\n");

        // 显示可用的串口
        ShowAvailablePorts();

        // 示例1：基本使用方法
        await Example1_BasicUsage();

        // 示例2：使用 Configure 方法
        await Example2_ConfigureMethod();

        // 示例3：依赖注入方式
        await Example3_DependencyInjection();

        Console.WriteLine("\n=== 示例完成 ===");
        Console.WriteLine("按任意键退出...");
        Console.ReadKey();
    }

    /// <summary>
    /// 显示系统中可用的串口
    /// </summary>
    static void ShowAvailablePorts()
    {
        Console.WriteLine("📡 系统中可用的串口:");
        var ports = System.IO.Ports.SerialPort.GetPortNames();
        if (ports.Length > 0)
        {
            foreach (var port in ports)
            {
                Console.WriteLine($"  - {port}");
            }
        }
        else
        {
            Console.WriteLine("  未找到可用的串口");
        }
        Console.WriteLine();
    }

    /// <summary>
    /// 示例1：基本使用方法（直接传入配置和日志记录器）
    /// </summary>
    static async Task Example1_BasicUsage()
    {
        Console.WriteLine("🚀 示例1：基本使用方法");
        Console.WriteLine("----------------------------------------");

        try
        {
            // 创建日志记录器
            using var loggerFactory = LoggerFactory.Create(builder =>
            {
                builder.AddConsole().SetMinimumLevel(LogLevel.Information);
            });
            var logger = loggerFactory.CreateLogger<SerialPortService>();

            // 创建配置
            var config = new SerialPortConfiguration
            {
                PortName = "COM1",  // 请根据实际情况修改端口名
                BaudRate = 9600,
                DataBits = 8,
                StopBits = System.IO.Ports.StopBits.One,
                Parity = System.IO.Ports.Parity.None,
                EnableAutoReconnect = true,
                ReconnectInterval = 3000,
                MaxReconnectAttempts = 3
            };

            // 创建服务（注意参数顺序：配置在前，日志记录器在后）
            using var serialPort = new SerialPortService(config, logger);

            // 订阅事件
            serialPort.DataReceived += (sender, e) =>
            {
                Console.WriteLine($"📨 接收到数据: {e.Data.ToText()}");
            };

            serialPort.StatusChanged += (sender, e) =>
            {
                Console.WriteLine($"🔄 状态变化: {e.OldState} -> {e.NewState}");
            };

            serialPort.ErrorOccurred += (sender, e) =>
            {
                Console.WriteLine($"❌ 发生错误: {e.Exception.Message}");
            };

            // 尝试打开连接
            Console.WriteLine($"🔌 尝试打开串口 {config.PortName}...");
            if (await serialPort.OpenAsync())
            {
                Console.WriteLine("✅ 串口打开成功");

                // 发送测试数据
                await serialPort.SendTextAsync("Hello, Serial Port!");
                Console.WriteLine("📤 已发送测试数据");

                // 等待一段时间以接收可能的响应
                await Task.Delay(2000);

                // 关闭连接
                await serialPort.CloseAsync();
                Console.WriteLine("🔌 串口已关闭");
            }
            else
            {
                Console.WriteLine("❌ 串口打开失败（可能是端口不存在或被占用）");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 示例1执行出错: {ex.Message}");
        }

        Console.WriteLine();
    }

    /// <summary>
    /// 示例2：使用 Configure 方法（先创建服务，后配置参数）
    /// </summary>
    static async Task Example2_ConfigureMethod()
    {
        Console.WriteLine("⚙️ 示例2：使用 Configure 方法");
        Console.WriteLine("----------------------------------------");

        try
        {
            // 创建简单的控制台日志记录器
            using var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
            var logger = loggerFactory.CreateLogger<SerialPortService>();

            // 先创建服务实例（只传入日志记录器）
            using var serialPort = new SerialPortService(logger);

            // 然后配置串口参数
            var config = new SerialPortConfiguration
            {
                PortName = "COM2",  // 使用不同的端口进行演示
                BaudRate = 115200,
                EnableAutoReconnect = false
            };

            serialPort.Configure(config);

            // 订阅事件
            serialPort.DataReceived += (sender, e) =>
            {
                Console.WriteLine($"📨 接收到数据: {e.Data.ToHexString(" ", true)}");
            };

            // 尝试打开连接
            Console.WriteLine($"🔌 尝试打开串口 {config.PortName}...");
            if (await serialPort.OpenAsync())
            {
                Console.WriteLine("✅ 串口打开成功");

                // 发送十六进制数据
                var hexData = SerialPortData.FromHexString("48 65 6C 6C 6F", config.PortName);
                await serialPort.SendAsync(hexData);
                Console.WriteLine("📤 已发送十六进制数据: 48 65 6C 6C 6F (Hello)");

                await Task.Delay(1000);
                await serialPort.CloseAsync();
                Console.WriteLine("🔌 串口已关闭");
            }
            else
            {
                Console.WriteLine("❌ 串口打开失败（可能是端口不存在或被占用）");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 示例2执行出错: {ex.Message}");
        }

        Console.WriteLine();
    }

    /// <summary>
    /// 示例3：依赖注入方式（推荐用于生产环境）
    /// </summary>
    static async Task Example3_DependencyInjection()
    {
        Console.WriteLine("🏗️ 示例3：依赖注入方式");
        Console.WriteLine("----------------------------------------");

        try
        {
            // 这个示例展示了如何在实际应用中使用依赖注入
            // 在实际项目中，您会在 Program.cs 或 Startup.cs 中配置服务

            using var serviceCollection = new Microsoft.Extensions.DependencyInjection.ServiceCollection();
            
            // 添加日志记录
            serviceCollection.AddLogging(builder => builder.AddConsole());
            
            // 注册 Alicres 串口服务
            serviceCollection.AddAlicresSerialPort();

            // 构建服务提供程序
            using var serviceProvider = serviceCollection.BuildServiceProvider();

            // 获取串口管理器
            var manager = serviceProvider.GetRequiredService<Alicres.SerialPort.Interfaces.ISerialPortManager>();

            // 创建配置
            var config = new SerialPortConfiguration
            {
                PortName = "COM3",
                BaudRate = 9600,
                EnableAutoReconnect = true
            };

            // 通过管理器创建串口服务
            var serialPort = manager.CreateSerialPort(config);

            Console.WriteLine($"🔌 通过管理器创建串口服务: {config.PortName}");
            Console.WriteLine("✅ 依赖注入配置完成");

            // 在实际应用中，您可以继续使用 serialPort 进行通讯
            // 这里只是演示创建过程
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 示例3执行出错: {ex.Message}");
        }

        Console.WriteLine();
    }
}
