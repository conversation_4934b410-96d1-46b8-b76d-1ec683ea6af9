using Alicres.SerialPort.Interfaces;
using Alicres.SerialPort.Models;
using Alicres.SerialPort.Services;
using Alicres.SerialPort.Exceptions;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using System.IO.Ports;
using Xunit;

namespace Alicres.SerialPort.Tests.Services;

/// <summary>
/// 串口服务测试类
/// </summary>
public class SerialPortServiceTests : IDisposable
{
    private readonly Mock<ILogger<SerialPortService>> _mockLogger;
    private readonly SerialPortConfiguration _configuration;
    private readonly SerialPortService _serialPortService;

    /// <summary>
    /// 构造函数
    /// </summary>
    public SerialPortServiceTests()
    {
        _mockLogger = new Mock<ILogger<SerialPortService>>();
        _configuration = new SerialPortConfiguration
        {
            PortName = "COM1",
            BaudRate = 9600,
            DataBits = 8,
            StopBits = StopBits.One,
            Parity = Parity.None,
            ReadTimeout = 1000,
            WriteTimeout = 1000,
            ReceiveBufferSize = 4096,
            SendBufferSize = 4096
        };
        _serialPortService = new SerialPortService(_configuration, _mockLogger.Object);
    }

    /// <summary>
    /// 测试构造函数正确初始化
    /// </summary>
    [Fact]
    public void Constructor_WithValidConfiguration_ShouldInitializeCorrectly()
    {
        // Assert
        _serialPortService.Configuration.Should().BeEquivalentTo(_configuration);
        _serialPortService.IsConnected.Should().BeFalse();
        _serialPortService.Status.Should().NotBeNull();
        _serialPortService.Status.PortName.Should().Be(_configuration.PortName);
        _serialPortService.Status.ConnectionState.Should().Be(SerialPortConnectionState.Disconnected);
    }

    /// <summary>
    /// 测试空配置抛出异常
    /// </summary>
    [Fact]
    public void Constructor_WithNullConfiguration_ShouldThrowException()
    {
        // Act & Assert
        var action = () => new SerialPortService(null!, _mockLogger.Object);
        action.Should().Throw<ArgumentNullException>();
    }

    /// <summary>
    /// 测试空日志记录器抛出异常
    /// </summary>
    [Fact]
    public void Constructor_WithNullLogger_ShouldThrowException()
    {
        // Act & Assert
        var action = () => new SerialPortService(_configuration, null!);
        action.Should().Throw<ArgumentNullException>();
    }

    /// <summary>
    /// 测试仅配置构造函数正确初始化
    /// </summary>
    [Fact]
    public void Constructor_WithConfigurationOnly_ShouldInitializeCorrectly()
    {
        // Act
        using var serialPort = new SerialPortService(_configuration);

        // Assert
        serialPort.Configuration.Should().BeEquivalentTo(_configuration);
        serialPort.IsConnected.Should().BeFalse();
        serialPort.Status.Should().NotBeNull();
        serialPort.Status.PortName.Should().Be(_configuration.PortName);
        serialPort.Status.ConnectionState.Should().Be(SerialPortConnectionState.Disconnected);
    }

    /// <summary>
    /// 测试仅配置构造函数空配置抛出异常
    /// </summary>
    [Fact]
    public void Constructor_WithNullConfigurationOnly_ShouldThrowException()
    {
        // Act & Assert
        var action = () => new SerialPortService((SerialPortConfiguration)null!);
        action.Should().Throw<ArgumentNullException>();
    }

    /// <summary>
    /// 测试配置串口参数
    /// </summary>
    [Fact]
    public void Configure_WithValidConfiguration_ShouldUpdateConfiguration()
    {
        // Arrange
        var newConfiguration = new SerialPortConfiguration
        {
            PortName = "COM2",
            BaudRate = 115200,
            DataBits = 8,
            StopBits = StopBits.One,
            Parity = Parity.None
        };

        // Act
        _serialPortService.Configure(newConfiguration);

        // Assert
        _serialPortService.Configuration.Should().BeEquivalentTo(newConfiguration);
        _serialPortService.Status.PortName.Should().Be(newConfiguration.PortName);
    }

    /// <summary>
    /// 测试空配置参数抛出异常
    /// </summary>
    [Fact]
    public void Configure_WithNullConfiguration_ShouldThrowException()
    {
        // Act & Assert
        var action = () => _serialPortService.Configure(null!);
        action.Should().Throw<ArgumentNullException>();
    }

    /// <summary>
    /// 测试获取可用端口
    /// </summary>
    [Fact]
    public void GetAvailablePorts_ShouldReturnPortArray()
    {
        // Act
        var ports = _serialPortService.GetAvailablePorts();

        // Assert
        ports.Should().NotBeNull();
        ports.Should().BeOfType<string[]>();
    }

    /// <summary>
    /// 测试发送空数据抛出异常
    /// </summary>
    [Fact]
    public async Task SendAsync_WithNullData_ShouldThrowException()
    {
        // Act & Assert
        var action = async () => await _serialPortService.SendAsync(null!);
        await action.Should().ThrowAsync<ArgumentNullException>();
    }

    /// <summary>
    /// 测试发送空文本抛出异常
    /// </summary>
    [Fact]
    public async Task SendTextAsync_WithNullText_ShouldThrowException()
    {
        // Act & Assert
        var action = async () => await _serialPortService.SendTextAsync(null!);
        await action.Should().ThrowAsync<ArgumentNullException>();
    }

    /// <summary>
    /// 测试读取数据空缓冲区抛出异常
    /// </summary>
    [Fact]
    public async Task ReadAsync_WithNullBuffer_ShouldThrowException()
    {
        // Act & Assert
        var action = async () => await _serialPortService.ReadAsync(null!, 0, 10);
        await action.Should().ThrowAsync<ArgumentNullException>();
    }

    /// <summary>
    /// 测试读取数据无效偏移量抛出异常
    /// </summary>
    [Fact]
    public async Task ReadAsync_WithInvalidOffset_ShouldThrowException()
    {
        // Arrange
        var buffer = new byte[10];

        // Act & Assert - 由于端口未打开，会抛出 SerialPortDataException
        var action = async () => await _serialPortService.ReadAsync(buffer, -1, 5);
        await action.Should().ThrowAsync<Exception>();
    }

    /// <summary>
    /// 测试读取数据无效计数抛出异常
    /// </summary>
    [Fact]
    public async Task ReadAsync_WithInvalidCount_ShouldThrowException()
    {
        // Arrange
        var buffer = new byte[10];

        // Act & Assert - 由于端口未打开，会抛出 SerialPortDataException
        var action = async () => await _serialPortService.ReadAsync(buffer, 0, -1);
        await action.Should().ThrowAsync<Exception>();
    }

    /// <summary>
    /// 测试读取数据缓冲区溢出抛出异常
    /// </summary>
    [Fact]
    public async Task ReadAsync_WithBufferOverflow_ShouldThrowException()
    {
        // Arrange
        var buffer = new byte[10];

        // Act & Assert - 由于端口未打开，会抛出 SerialPortDataException
        var action = async () => await _serialPortService.ReadAsync(buffer, 5, 10);
        await action.Should().ThrowAsync<Exception>();
    }

    /// <summary>
    /// 测试缓冲区管理功能
    /// </summary>
    [Fact]
    public void BufferManagement_WhenAdvancedBufferingDisabled_ShouldReturnDefaults()
    {
        // Act & Assert
        _serialPortService.GetBufferStatistics().Should().BeNull();
        _serialPortService.GetQueueLength().Should().Be(0);
        _serialPortService.GetQueueUsagePercentage().Should().Be(0);
        _serialPortService.DequeueDataBatch().Should().BeEmpty();
    }

    /// <summary>
    /// 测试缓冲区清理
    /// </summary>
    [Fact]
    public void ClearDataQueue_ShouldNotThrow()
    {
        // Act & Assert
        var action = () => _serialPortService.ClearDataQueue();
        action.Should().NotThrow();
    }

    /// <summary>
    /// 测试清空接收缓冲区
    /// </summary>
    [Fact]
    public void ClearReceiveBuffer_WhenNotConnected_ShouldNotThrow()
    {
        // Act & Assert
        var action = () => _serialPortService.ClearReceiveBuffer();
        action.Should().NotThrow();
    }

    /// <summary>
    /// 测试清空发送缓冲区
    /// </summary>
    [Fact]
    public void ClearSendBuffer_WhenNotConnected_ShouldNotThrow()
    {
        // Act & Assert
        var action = () => _serialPortService.ClearSendBuffer();
        action.Should().NotThrow();
    }

    /// <summary>
    /// 测试获取接收缓冲区字节数
    /// </summary>
    [Fact]
    public void GetBytesToRead_WhenNotConnected_ShouldReturnZero()
    {
        // Act & Assert
        _serialPortService.GetBytesToRead().Should().Be(0);
    }

    /// <summary>
    /// 测试获取发送缓冲区字节数
    /// </summary>
    [Fact]
    public void GetBytesToWrite_WhenNotConnected_ShouldReturnZero()
    {
        // Act & Assert
        _serialPortService.GetBytesToWrite().Should().Be(0);
    }

    /// <summary>
    /// 测试释放资源
    /// </summary>
    [Fact]
    public void Dispose_ShouldNotThrow()
    {
        // Act & Assert
        var action = () => _serialPortService.Dispose();
        action.Should().NotThrow();
    }

    /// <summary>
    /// 释放测试资源
    /// </summary>
    public void Dispose()
    {
        _serialPortService?.Dispose();
    }
}
